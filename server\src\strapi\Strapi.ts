import axios from "axios";
import qs from "qs";

interface IStrapiResponse {
	data: {
		data: any[];
		meta: {
			pagination: {
				total: number;
			};
		};
	};
}

interface IWebContent {
	id: number;
	documentId: string;
	slug: string;
	title: string;
	locale: string;
	content: {
		__component: string;
		id: number;
		Header: string;
		Content: string;
	}[];
}

function formatComponentString(r: IWebContent): IWebContent {
	const newContent = r.content.map((c) => {
		c.__component = c.__component.replace("web-content-parts.", "");
		return c;
	});

	const newR = {
		...r,
		content: newContent,
	};

	return newR;
}

async function getWebContent(
	slug: string,
): Promise<IWebContent | { error: string }> {
	const qsSlug = qs.stringify(
		{
			filters: {
				slug: {
					$eq: slug,
				},
			},
			populate: {
				content: {
					populate: "*",
				},
			},
		},
		{
			encodeValuesOnly: true,
		},
	);
	const r: IStrapiResponse = await axios.get(
		`${Bun.env.CMS_URL}/api/web-contents?${qsSlug}`,
		{
			headers: {
				Authorization: `Bearer ${Bun.env.CMS_KEY}`,
			},
		},
	);

	if (r.data.meta.pagination.total === 0) {
		return {
			error: "Not Found",
		};
	}

	const formatted = formatComponentString(r.data.data[0]);

	return formatted;
}

export { getWebContent, type IWebContent };
