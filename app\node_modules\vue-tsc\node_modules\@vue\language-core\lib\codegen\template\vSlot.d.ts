import * as CompilerDOM from '@vue/compiler-dom';
import type { Code } from '../../types';
import type { TemplateCodegenContext } from './context';
import type { TemplateCodegenOptions } from './index';
export declare function generateVSlot(options: TemplateCodegenOptions, ctx: TemplateCodegenContext, node: CompilerDOM.ElementNode, slotDir: CompilerDOM.DirectiveNode): Generator<Code>;
export declare function generateImplicitDefaultSlot(ctx: TemplateCodegenContext, node: CompilerDOM.ElementNode): Generator<Code, void, any>;
